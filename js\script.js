// ===== BARANOF LODGE WEBSITE FUNCTIONALITY =====

document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM Content Loaded - Initializing website...');
    initializeWebsite();
});

// Also try window.onload as a backup
window.addEventListener('load', function () {
    console.log('Window loaded - Double-checking initialization...');
    if (!window.websiteInitialized) {
        initializeWebsite();
    }
});

function initializeWebsite() {
    console.log('initializeWebsite called');
    window.websiteInitialized = true;

    // Add a small delay to ensure DOM is fully ready
    setTimeout(() => {
        setupButtonHandlers();
        setupResponsiveLayout();
        setupHotelLinks();
        setupScrollEffects();
    }, 100);
}

// ===== BUTTON HANDLERS =====
function setupButtonHandlers() {
    console.log('Setting up button handlers...');

    // Book Now buttons
    const bookNowButtons = document.querySelectorAll('#bookNowBtn, #bookNowNav');
    console.log('Found Book Now buttons:', bookNowButtons.length);
    bookNowButtons.forEach((btn, index) => {
        console.log(`Setting up Book Now button ${index}:`, btn);
        btn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Book Now clicked!');
            handleBookNow();
        });

        // Also add a direct onclick as backup
        btn.onclick = function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Book Now clicked via onclick!');
            handleBookNow();
        };
    });

    // Play Game button
    const playGameBtn = document.getElementById('playGameBtn');
    console.log('Found Play Game button:', playGameBtn);
    if (playGameBtn) {
        playGameBtn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Play Game clicked!');
            handlePlayGame();
        });

        // Also add a direct onclick as backup
        playGameBtn.onclick = function (e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Play Game clicked via onclick!');
            handlePlayGame();
        };
    } else {
        console.error('Play Game button not found!');
        // Try to find it again after a delay
        setTimeout(() => {
            const retryBtn = document.getElementById('playGameBtn');
            if (retryBtn) {
                console.log('Found Play Game button on retry:', retryBtn);
                retryBtn.onclick = function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Play Game clicked via retry onclick!');
                    handlePlayGame();
                };
            }
        }, 500);
    }
}

function handleBookNow() {
    // For now, show an alert. In production, this would redirect to booking system
    alert('Booking system would open here!\n\nCall (************* to make a reservation.');

    // Example of what this might look like in production:
    // window.open('https://booking.baranovlodge.com', '_blank');
}

function handlePlayGame() {
    console.log('handlePlayGame called');

    // Move the Book Now button when game is activated
    const ctaButtons = document.getElementById('ctaButtons');
    const bookNowBtn = document.getElementById('bookNowBtn');

    if (window.innerWidth <= 768) {
        // Mobile: move to top
        ctaButtons.style.flexDirection = 'column-reverse';
    } else {
        // Desktop: move to right
        ctaButtons.style.justifyContent = 'flex-end';
        bookNowBtn.style.order = '2';
    }

    // Start the game (handled by game.js)
    console.log('Checking for pirateGame:', window.pirateGame);
    if (window.pirateGame) {
        console.log('Starting game...');
        window.pirateGame.startGame();
    } else {
        console.error('PirateGame not found! Trying to initialize...');
        // Try to initialize the game if it's not ready
        setTimeout(() => {
            if (window.pirateGame) {
                window.pirateGame.startGame();
            } else {
                alert('Game is still loading. Please try again in a moment.');
            }
        }, 1000);
    }
}

// ===== RESPONSIVE LAYOUT =====
function setupResponsiveLayout() {
    // Handle window resize
    window.addEventListener('resize', function () {
        updateBackgroundImage();
        updateMobileControls();
    });

    // Initial setup
    updateBackgroundImage();
    updateMobileControls();
}

function updateBackgroundImage() {
    const heroBackground = document.querySelector('.hero-background');
    const gameWorld = document.getElementById('gameWorld');

    if (window.innerWidth <= 768) {
        // Mobile: use portrait image
        if (heroBackground) {
            heroBackground.style.backgroundImage = "url('/images/hero/map_portrait.webp')";
        }
        if (gameWorld) {
            gameWorld.style.backgroundImage = "url('/images/hero/map_portrait.webp')";
        }
    } else {
        // Desktop: use landscape image
        if (heroBackground) {
            heroBackground.style.backgroundImage = "url('/images/hero/map_landscape.webp')";
        }
        if (gameWorld) {
            gameWorld.style.backgroundImage = "url('/images/hero/map_landscape.webp')";
        }
    }
}

function updateMobileControls() {
    const mobileControls = document.getElementById('mobileControls');
    if (mobileControls) {
        if (window.innerWidth <= 768) {
            mobileControls.style.display = 'flex';
        } else {
            mobileControls.style.display = 'none';
        }
    }
}

// ===== HOTEL LINKS =====
function setupHotelLinks() {
    const hotelLinks = document.querySelectorAll('.hotel-link');
    hotelLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const hotel = this.dataset.hotel;
            handleHotelClick(hotel);
        });
    });
}

// ===== SCROLL EFFECTS =====
function setupScrollEffects() {
    // Add scroll-based navbar transparency
    window.addEventListener('scroll', function () {
        const navbar = document.querySelector('.pirate-nav');
        if (navbar) {
            if (window.scrollY > 50) {
                navbar.style.background = 'linear-gradient(135deg, rgba(139, 69, 19, 0.95), rgba(30, 58, 95, 0.95))';
            } else {
                navbar.style.background = 'linear-gradient(135deg, rgba(139, 69, 19, 0.9), rgba(30, 58, 95, 0.9))';
            }
        }
    });
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    // Create a simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--pirate-brown);
        color: var(--pirate-cream);
        padding: 1rem 2rem;
        border-radius: 10px;
        border: 2px solid var(--pirate-gold);
        z-index: 9999;
        font-family: 'Cinzel', serif;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
function setupAccessibility() {
    // Add keyboard navigation for game controls
    document.addEventListener('keydown', function (e) {
        // ESC key to close game
        if (e.key === 'Escape') {
            const gameContainer = document.getElementById('gameContainer');
            if (gameContainer && !gameContainer.classList.contains('hidden')) {
                if (window.pirateGame) {
                    window.pirateGame.endGame();
                }
            }
        }
    });

    // Add focus indicators for better keyboard navigation
    const focusableElements = document.querySelectorAll('button, a, input, [tabindex]');
    focusableElements.forEach(element => {
        element.addEventListener('focus', function () {
            this.style.outline = '2px solid var(--pirate-gold)';
            this.style.outlineOffset = '2px';
        });

        element.addEventListener('blur', function () {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });
}

// Initialize accessibility features
document.addEventListener('DOMContentLoaded', setupAccessibility);

// ===== PERFORMANCE OPTIMIZATIONS =====
function optimizePerformance() {
    // Lazy load images that are not immediately visible
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', optimizePerformance);
