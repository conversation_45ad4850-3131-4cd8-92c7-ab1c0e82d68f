<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baranof Lodge - Map Editor</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400..900&family=Roboto+Slab:wght@100..900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="css/map.css?ver=1.0">
</head>

<body>
    <!-- Header -->
    <div class="editor-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="editor-title mb-0">
                        <i class="fas fa-map"></i>Baranof Lodge Map Editor
                    </h1>
                    <p class="mb-0 text-muted">Create and edit game maps with item placement</p>
                </div>
                <div class="col-auto">
                    <a href="index.html" class="btn btn-pirate">
                        <i class="fas fa-home"></i>Back to Game
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <div class="row">
            <!-- Left Panel - Controls -->
            <div class="col-lg-4">
                <div class="controls-panel">
                    <h3 class="text-center mb-4" style="color: var(--pirate-gold); font-family: 'Cinzel', serif;">
                        <i class="fas fa-cogs"></i> Map Controls
                    </h3>

                    <!-- Image Upload -->
                    <div class="mb-4">
                        <label for="imageUpload" class="form-label">
                            <i class="fas fa-image"></i> Load Map Image
                        </label>
                        <input type="file" class="form-control" id="imageUpload" accept="image/*">
                        <small class="text-muted">Supported formats: JPG, PNG, WebP</small>
                    </div>

                    <!-- Tile Size Info -->
                    <div class="tile-size-info">
                        <strong>Tile Size: 32x32 pixels</strong><br>
                        <small>Click on any tile to place an item</small>
                    </div>

                    <!-- Grid Toggle -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showGrid" checked>
                            <label class="form-check-label" for="showGrid">
                                Show Grid Overlay
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="showWaterDetection" checked>
                            <label class="form-check-label" for="showWaterDetection">
                                Enable Water Detection
                            </label>
                        </div>
                        <small class="text-muted">Automatically moves items from water to land</small>
                    </div>

                    <!-- Current Items List -->
                    <div class="mb-4">
                        <h5 style="color: var(--pirate-gold);">
                            <i class="fas fa-list"></i> Placed Items (<span id="itemCount">0</span>)
                        </h5>
                        <div id="itemsList" class="items-list">
                            <p class="text-muted text-center">No items placed yet. Click on the map to add items.</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <button class="btn btn-pirate" id="clearAllBtn">
                            <i class="fas fa-trash"></i> Clear All Items
                        </button>
                        <button class="btn btn-pirate" id="saveProjectBtn">
                            <i class="fas fa-save"></i> Save Project
                        </button>
                        <button class="btn btn-pirate" id="loadProjectBtn">
                            <i class="fas fa-folder-open"></i> Load Project
                        </button>
                        <input type="file" id="projectFileInput" accept=".json" style="display: none;">
                    </div>
                </div>
            </div>

            <!-- Center Panel - Canvas -->
            <div class="col-lg-5">
                <div class="text-center">
                    <h4 style="color: var(--pirate-gold); font-family: 'Cinzel', serif;" class="mb-3">
                        <i class="fas fa-map-marked-alt"></i> Map Canvas
                    </h4>
                    <div class="canvas-container" id="canvasContainer">
                        <canvas id="mapCanvas" width="800" height="600"></canvas>
                        <canvas id="gridCanvas" class="grid-overlay" width="800" height="600"></canvas>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            Canvas Size: <span id="canvasSize">800x600</span> |
                            Grid: <span id="gridSize">25x19 tiles</span>
                        </small>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Code Output -->
            <div class="col-lg-3">
                <div class="controls-panel">
                    <h4 style="color: var(--pirate-gold); font-family: 'Cinzel', serif;" class="mb-3">
                        <i class="fas fa-code"></i> Generated Code
                    </h4>
                    <p class="text-muted small mb-3">
                        Copy this code and replace the <code>itemTypes</code> constant in <code>game.js</code>
                    </p>
                    <div class="code-output" id="codeOutput">
                        // No items placed yet
                        const itemTypes = {
                        treasures: [],
                        trash: [],
                        other: []
                        };
                    </div>
                    <div class="mt-3 d-grid">
                        <button class="btn btn-pirate" id="copyCodeBtn">
                            <i class="fas fa-copy"></i> Copy to Clipboard
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Creation Modal -->
    <div class="modal fade" id="itemModal" tabindex="-1" aria-labelledby="itemModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background: var(--pirate-black); border: 3px solid var(--pirate-gold);">
                <div class="modal-header" style="border-bottom: 2px solid var(--pirate-gold);">
                    <h5 class="modal-title" id="itemModalLabel"
                        style="color: var(--pirate-gold); font-family: 'Cinzel', serif;">
                        <i class="fas fa-plus-circle"></i> Add New Item
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"
                        style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <form id="itemForm">
                        <div class="mb-3">
                            <label for="itemType" class="form-label">Item Type</label>
                            <select class="form-select" id="itemType" required>
                                <option value="">Select item type...</option>
                                <option value="treasures">Treasure (100 points)</option>
                                <option value="trash">Trash (50 points)</option>
                                <option value="other">Other (75 points)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="itemName" class="form-label">Item Name</label>
                            <input type="text" class="form-control" id="itemName" required
                                placeholder="Enter item name...">
                        </div>
                        <div class="mb-3">
                            <label for="itemEmoji" class="form-label">Item Emoji</label>
                            <input type="text" class="form-control" id="itemEmoji" required placeholder="🏴‍☠️"
                                maxlength="2">
                            <small class="text-muted">Single emoji character</small>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <label for="itemX" class="form-label">X Position (Tile)</label>
                                <input type="number" class="form-control" id="itemX" readonly>
                            </div>
                            <div class="col-6">
                                <label for="itemY" class="form-label">Y Position (Tile)</label>
                                <input type="number" class="form-control" id="itemY" readonly>
                            </div>
                        </div>
                        <div class="mt-3">
                            <label for="itemCount" class="form-label">Count</label>
                            <input type="number" class="form-control" id="itemCount" min="1" value="1">
                            <small class="text-muted">Number of this item to place</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top: 2px solid var(--pirate-gold);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-pirate" id="addItemBtn">
                        <i class="fas fa-plus"></i> Add Item
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/editor.js"></script>

</body>

</html>