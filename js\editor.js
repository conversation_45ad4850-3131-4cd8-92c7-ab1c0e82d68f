class MapEditor {
    constructor() {
        this.tileSize = 32;
        this.canvas = document.getElementById('mapCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridCanvas = document.getElementById('gridCanvas');
        this.gridCtx = this.gridCanvas.getContext('2d');

        this.currentImage = null;
        this.items = [];
        this.showGrid = true;
        this.waterDetectionEnabled = true;
        this.mapAnalysisCanvas = null;
        this.mapAnalysisCtx = null;

        this.initializeEventListeners();
        this.drawGrid();
        this.updateItemsList();
        this.generateCode();
    }

    initializeEventListeners() {
        // Image upload
        document.getElementById('imageUpload').addEventListener('change', (e) => {
            this.loadImage(e.target.files[0]);
        });

        // Canvas click
        this.canvas.addEventListener('click', (e) => {
            this.handleCanvasClick(e);
        });

        // Grid toggle
        document.getElementById('showGrid').addEventListener('change', (e) => {
            this.showGrid = e.target.checked;
            this.toggleGrid();
        });

        // Water detection toggle
        document.getElementById('showWaterDetection').addEventListener('change', (e) => {
            this.waterDetectionEnabled = e.target.checked;
        });

        // Modal form submission
        document.getElementById('addItemBtn').addEventListener('click', () => {
            this.addItem();
        });

        // Clear all items
        document.getElementById('clearAllBtn').addEventListener('click', () => {
            this.clearAllItems();
        });

        // Copy code
        document.getElementById('copyCodeBtn').addEventListener('click', () => {
            this.copyCode();
        });

        // Save/Load project
        document.getElementById('saveProjectBtn').addEventListener('click', () => {
            this.saveProject();
        });

        document.getElementById('loadProjectBtn').addEventListener('click', () => {
            document.getElementById('projectFileInput').click();
        });

        document.getElementById('projectFileInput').addEventListener('change', (e) => {
            this.loadProject(e.target.files[0]);
        });
    }

    loadImage(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.currentImage = img;
                this.resizeCanvasToImage(img);
                this.setupMapAnalysis(img);
                this.drawImage();
                this.drawGrid();
                this.redrawItems();
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    resizeCanvasToImage(img) {
        // Calculate canvas size to fit image while maintaining aspect ratio
        const maxWidth = 800;
        const maxHeight = 600;

        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width *= ratio;
            height *= ratio;
        }

        // Round to nearest tile size for clean grid
        width = Math.round(width / this.tileSize) * this.tileSize;
        height = Math.round(height / this.tileSize) * this.tileSize;

        this.canvas.width = width;
        this.canvas.height = height;
        this.gridCanvas.width = width;
        this.gridCanvas.height = height;

        // Update UI
        document.getElementById('canvasSize').textContent = `${width}x${height}`;
        document.getElementById('gridSize').textContent = `${width / this.tileSize}x${height / this.tileSize} tiles`;
    }

    setupMapAnalysis(img) {
        // Create a hidden canvas to analyze the map image for water detection
        this.mapAnalysisCanvas = document.createElement('canvas');
        this.mapAnalysisCtx = this.mapAnalysisCanvas.getContext('2d');

        // Set canvas size to match the displayed canvas
        this.mapAnalysisCanvas.width = this.canvas.width;
        this.mapAnalysisCanvas.height = this.canvas.height;

        // Draw the image to analyze pixel colors
        this.mapAnalysisCtx.drawImage(img, 0, 0, this.mapAnalysisCanvas.width, this.mapAnalysisCanvas.height);
    }

    isWaterTile(tileX, tileY) {
        if (!this.mapAnalysisCanvas) return false;

        // Get the center pixel of the tile
        const pixelX = Math.floor(tileX * this.tileSize + this.tileSize / 2);
        const pixelY = Math.floor(tileY * this.tileSize + this.tileSize / 2);

        // Make sure we're within bounds
        if (pixelX < 0 || pixelX >= this.mapAnalysisCanvas.width ||
            pixelY < 0 || pixelY >= this.mapAnalysisCanvas.height) {
            return true; // Consider out of bounds as water
        }

        // Get pixel data
        const imageData = this.mapAnalysisCtx.getImageData(pixelX, pixelY, 1, 1);
        const [r, g, b] = imageData.data;

        // Check if pixel is blue-ish (water)
        // Blue should be dominant and significantly higher than red/green
        const isBlue = b > r + 30 && b > g + 30 && b > 100;

        // Also check for light blue/cyan colors
        const isCyan = b > 150 && g > 100 && r < 150;

        return isBlue || isCyan;
    }

    findLandTile() {
        // Try to find a random land tile (non-water)
        let attempts = 0;
        const maxAttempts = 100;
        const maxTileX = Math.floor(this.canvas.width / this.tileSize);
        const maxTileY = Math.floor(this.canvas.height / this.tileSize);

        while (attempts < maxAttempts) {
            const tileX = Math.floor(Math.random() * (maxTileX - 2)) + 1;
            const tileY = Math.floor(Math.random() * (maxTileY - 2)) + 1;

            if (!this.isWaterTile(tileX, tileY)) {
                return { x: tileX, y: tileY };
            }
            attempts++;
        }

        // Fallback to center if no land found
        return { x: Math.floor(maxTileX / 2), y: Math.floor(maxTileY / 2) };
    }

    drawImage() {
        if (!this.currentImage) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        this.ctx.drawImage(this.currentImage, 0, 0, this.canvas.width, this.canvas.height);
    }

    drawGrid() {
        this.gridCtx.clearRect(0, 0, this.gridCanvas.width, this.gridCanvas.height);

        if (!this.showGrid) return;

        this.gridCtx.strokeStyle = '#FFD700';
        this.gridCtx.lineWidth = 1;
        this.gridCtx.globalAlpha = 0.5;

        // Draw vertical lines
        for (let x = 0; x <= this.gridCanvas.width; x += this.tileSize) {
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(x, 0);
            this.gridCtx.lineTo(x, this.gridCanvas.height);
            this.gridCtx.stroke();
        }

        // Draw horizontal lines
        for (let y = 0; y <= this.gridCanvas.height; y += this.tileSize) {
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(0, y);
            this.gridCtx.lineTo(this.gridCanvas.width, y);
            this.gridCtx.stroke();
        }

        this.gridCtx.globalAlpha = 1;
    }

    toggleGrid() {
        this.gridCanvas.style.display = this.showGrid ? 'block' : 'none';
        this.drawGrid();
    }

    handleCanvasClick(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Convert to tile coordinates
        let tileX = Math.floor(x / this.tileSize);
        let tileY = Math.floor(y / this.tileSize);

        // Check if clicked position is water (only if water detection is enabled)
        if (this.waterDetectionEnabled && this.isWaterTile(tileX, tileY)) {
            // Find a nearby land tile
            const landPosition = this.findLandTile();
            tileX = landPosition.x;
            tileY = landPosition.y;

            // Show warning to user
            alert(`⚠️ Water detected! Items cannot be placed in water.\nMoved to nearest land position: (${tileX}, ${tileY})`);
        }

        // Show modal with coordinates
        document.getElementById('itemX').value = tileX;
        document.getElementById('itemY').value = tileY;

        // Reset form
        document.getElementById('itemForm').reset();
        document.getElementById('itemX').value = tileX;
        document.getElementById('itemY').value = tileY;
        document.getElementById('itemCount').value = 1;

        const modal = new bootstrap.Modal(document.getElementById('itemModal'));
        modal.show();
    }

    addItem() {
        const form = document.getElementById('itemForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const itemData = {
            type: document.getElementById('itemType').value,
            name: document.getElementById('itemName').value,
            emoji: document.getElementById('itemEmoji').value,
            x: parseInt(document.getElementById('itemX').value),
            y: parseInt(document.getElementById('itemY').value),
            count: parseInt(document.getElementById('itemCount').value)
        };

        this.items.push(itemData);
        this.updateItemsList();
        this.generateCode();
        this.redrawItems();

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('itemModal'));
        modal.hide();
    }

    removeItem(index) {
        this.items.splice(index, 1);
        this.updateItemsList();
        this.generateCode();
        this.redrawItems();
    }

    updateItemsList() {
        const container = document.getElementById('itemsList');
        const count = document.getElementById('itemCount');

        count.textContent = this.items.length;

        if (this.items.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No items placed yet. Click on the map to add items.</p>';
            return;
        }

        container.innerHTML = this.items.map((item, index) => `
            <div class="item-entry">
                <div class="item-info">
                    <span class="item-emoji">${item.emoji}</span>
                    <div>
                        <strong>${item.name}</strong><br>
                        <small class="text-muted">${item.type} • (${item.x}, ${item.y}) • Count: ${item.count}</small>
                    </div>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="mapEditor.removeItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `).join('');
    }

    redrawItems() {
        // Clear existing item markers and redraw them
        // This would be implemented if we want to show items on the canvas
        // For now, items are only shown in the list
    }

    generateCode() {
        const grouped = {
            treasures: [],
            trash: [],
            other: []
        };

        this.items.forEach(item => {
            if (grouped[item.type]) {
                grouped[item.type].push({
                    name: item.name,
                    emoji: item.emoji,
                    x: item.x,
                    y: item.y,
                    count: item.count
                });
            }
        });

        const code = `const itemTypes = {
// Treasures (worth 100 points each)
treasures: [
${grouped.treasures.map(item => `        { name: '${item.name}', emoji: '${item.emoji}', x: ${item.x}, y: ${item.y}, count: ${item.count} }`).join(',\n')}
],
// Trash (worth 50 points each)
trash: [
${grouped.trash.map(item => `        { name: '${item.name}', emoji: '${item.emoji}', x: ${item.x}, y: ${item.y}, count: ${item.count} }`).join(',\n')}
],
// Other items (worth 75 points each)
other: [
${grouped.other.map(item => `        { name: '${item.name}', emoji: '${item.emoji}', x: ${item.x}, y: ${item.y}, count: ${item.count} }`).join(',\n')}
]
};`;

        document.getElementById('codeOutput').textContent = code;
    }

    clearAllItems() {
        if (this.items.length === 0) return;

        if (confirm('Are you sure you want to clear all items? This cannot be undone.')) {
            this.items = [];
            this.updateItemsList();
            this.generateCode();
            this.redrawItems();
        }
    }

    copyCode() {
        const codeText = document.getElementById('codeOutput').textContent;
        navigator.clipboard.writeText(codeText).then(() => {
            // Show success feedback
            const btn = document.getElementById('copyCodeBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            btn.classList.add('btn-success');
            btn.classList.remove('btn-pirate');

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-pirate');
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy code:', err);
            alert('Failed to copy code to clipboard. Please copy manually.');
        });
    }

    saveProject() {
        const projectData = {
            items: this.items,
            canvasSize: {
                width: this.canvas.width,
                height: this.canvas.height
            },
            imageData: this.currentImage ? this.canvas.toDataURL() : null,
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `map-project-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    loadProject(file) {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const projectData = JSON.parse(e.target.result);

                // Load items
                this.items = projectData.items || [];

                // Load image if available
                if (projectData.imageData) {
                    const img = new Image();
                    img.onload = () => {
                        this.currentImage = img;
                        this.canvas.width = projectData.canvasSize.width;
                        this.canvas.height = projectData.canvasSize.height;
                        this.gridCanvas.width = projectData.canvasSize.width;
                        this.gridCanvas.height = projectData.canvasSize.height;

                        this.drawImage();
                        this.drawGrid();
                        this.updateItemsList();
                        this.generateCode();
                        this.redrawItems();

                        // Update UI
                        document.getElementById('canvasSize').textContent = `${this.canvas.width}x${this.canvas.height}`;
                        document.getElementById('gridSize').textContent = `${this.canvas.width / this.tileSize}x${this.canvas.height / this.tileSize} tiles`;
                    };
                    img.src = projectData.imageData;
                } else {
                    this.updateItemsList();
                    this.generateCode();
                }

                alert('Project loaded successfully!');
            } catch (error) {
                console.error('Error loading project:', error);
                alert('Error loading project file. Please check the file format.');
            }
        };
        reader.readAsText(file);
    }
}

// Initialize the map editor when the page loads
let mapEditor;
document.addEventListener('DOMContentLoaded', () => {
    mapEditor = new MapEditor();
});