/* ===== GAME STYLES ===== */

/* Game Container */
.game-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem 1rem 2rem;
    background: linear-gradient(135deg, var(--pirate-brown-alpha), var(--pirate-blue-alpha));
}

.game-stats {
    display: flex;
    gap: 2rem;
}

.score-display,
.timer-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cinzel', serif;
    font-weight: 700;
}

.score-display .label,
.timer-display .label {
    color: var(--pirate-cream);
    font-size: 1.2rem;
}

.score-display .value,
.timer-display .value {
    color: var(--pirate-gold);
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    min-width: 80px;
    text-align: center;
    background: var(--pirate-black-alpha);
    padding: 0.3rem 0.8rem;
    border-radius: 10px;
    border: 1px solid var(--pirate-gold);
}

.btn-close-game {
    background: var(--pirate-red);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-close-game:hover {
    background: #ff0000;
    transform: scale(1.1);
}

/* Game World */
.game-world {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-image: url('/images/hero/map_landscape.webp');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

#gameCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Character */
.character {
    position: absolute;
    width: 32px;
    height: 32px;
    background-image: url('/images/characters/character_walk.webp');
    background-size: 576px 32px;
    /* 18 frames * 32px width */
    background-repeat: no-repeat;
    z-index: 10;
    transition: all 0.1s ease;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

.character.walking {
    animation: walk 0.8s steps(18) infinite;
}

.character.jumping {
    animation: jump 0.5s ease-out;
}

@keyframes walk {
    from {
        background-position-x: 0;
    }

    to {
        background-position-x: -576px;
    }
}

@keyframes jump {
    0% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-20px);
    }

    100% {
        transform: translateY(0);
    }
}

/* Game Items */
.game-items {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 5;
    pointer-events: none;
}

.game-item {
    position: absolute;
    width: 24px;
    height: 24px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 5;
    transition: all 0.3s ease;
    cursor: pointer;
    pointer-events: auto;
}

.game-item.found {
    opacity: 0;
    transform: scale(0);
}

.game-item.treasure {
    filter: drop-shadow(0 0 8px var(--pirate-gold));
    animation: treasure-glow 2s ease-in-out infinite alternate;
}

.game-item.trash {
    filter: drop-shadow(0 0 4px var(--pirate-red));
}

.game-item.other {
    filter: drop-shadow(0 0 4px var(--pirate-blue));
}

@keyframes treasure-glow {
    from {
        filter: drop-shadow(0 0 8px var(--pirate-gold)) brightness(1);
    }

    to {
        filter: drop-shadow(0 0 12px var(--pirate-gold)) brightness(1.2);
    }
}

/* Found Items Panel */
.found-items-panel {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: var(--pirate-black-alpha);
    border: 2px solid var(--pirate-gold);
    border-radius: 15px;
    padding: 1rem;
    max-width: 250px;
    max-height: 300px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
}

.found-items-panel h4 {
    color: var(--pirate-gold);
    font-family: 'Cinzel', serif;
    margin-bottom: 0.5rem;
    text-align: center;
    font-size: 1.1rem;
}

.found-items-list {
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 0.9rem;
}

.found-items-list li {
    color: var(--pirate-cream);
    padding: 0.3rem 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
    transition: all 0.3s ease;
}

.found-items-list li:last-child {
    border-bottom: none;
}

.found-items-list li.found {
    text-decoration: line-through;
    color: var(--pirate-gold);
    opacity: 0.7;
}

.found-items-list li.treasure::before {
    content: '💰 ';
}

.found-items-list li.trash::before {
    content: '🗑️ ';
}

.found-items-list li.other::before {
    content: '🔍 ';
}

/* Mobile Controls */
.mobile-controls {
    position: absolute;
    bottom: 20px;
    left: 20px;
    display: none;
    gap: 2rem;
    align-items: flex-end;
}

.dpad {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 5px;
    width: 120px;
    height: 120px;
}

.dpad .up {
    grid-column: 2;
    grid-row: 1;
}

.dpad .middle-row {
    grid-column: 1 / 4;
    grid-row: 2;
    display: flex;
    justify-content: space-between;
}

.dpad .down {
    grid-column: 2;
    grid-row: 3;
}

.control-btn {
    background: var(--pirate-brown-alpha);
    border: 2px solid var(--pirate-gold);
    border-radius: 10px;
    color: var(--pirate-cream);
    font-size: 1.2rem;
    font-weight: bold;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.control-btn.jump {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 0.9rem;
}

.control-btn:active {
    background: var(--pirate-gold);
    color: var(--pirate-black);
    transform: scale(0.95);
}

/* Game Over Modal */
.pirate-modal .modal-content {
    background: linear-gradient(135deg, var(--pirate-black), var(--pirate-dark-brown));
    border: 3px solid var(--pirate-gold);
    border-radius: 20px;
    color: var(--pirate-cream);
}

.pirate-modal .modal-header {
    border-bottom: 2px solid var(--pirate-gold);
}

.pirate-modal .modal-title {
    color: var(--pirate-gold);
    font-family: 'Cinzel', serif;
    font-weight: 700;
}

.score-highlight {
    color: var(--pirate-gold);
    font-family: 'Cinzel', serif;
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.leaderboard {
    background: var(--pirate-brown-alpha);
    border: 1px solid var(--pirate-gold);
    border-radius: 10px;
    padding: 1rem;
}

.leaderboard h6 {
    color: var(--pirate-gold);
    font-family: 'Cinzel', serif;
    margin-bottom: 0.5rem;
}

#leaderboardList {
    color: var(--pirate-cream);
    font-size: 0.9rem;
}

#leaderboardList li {
    padding: 0.2rem 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.3);
}

#leaderboardList li:last-child {
    border-bottom: none;
}

/* Responsive Design for Game */
@media (max-width: 768px) {
    .game-world {
        background-image: url('/images/hero/map_portrait.webp');
    }

    .mobile-controls {
        display: flex;
    }

    .game-header {
        padding: 0.5rem 1rem;
    }

    .game-stats {
        gap: 1rem;
    }

    .score-display .value,
    .timer-display .value {
        font-size: 1.2rem;
        min-width: 60px;
        padding: 0.2rem 0.5rem;
    }

    .found-items-panel {
        bottom: 160px;
        right: 10px;
        max-width: 200px;
        padding: 0.8rem;
    }

    .found-items-panel h4 {
        font-size: 1rem;
    }

    .found-items-list {
        font-size: 0.8rem;
    }
}

/* Item Found Animation */
@keyframes itemFound {
    0% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }

    50% {
        transform: scale(1.5) translateY(-20px);
        opacity: 0.8;
    }

    100% {
        transform: scale(0) translateY(-40px);
        opacity: 0;
    }
}

.item-found-animation {
    animation: itemFound 0.8s ease-out forwards;
}

/* Score popup animation */
.score-popup {
    position: absolute;
    color: var(--pirate-gold);
    font-family: 'Cinzel', serif;
    font-weight: 700;
    font-size: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    pointer-events: none;
    z-index: 20;
    animation: scorePopup 1.5s ease-out forwards;
}

@keyframes scorePopup {
    0% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }

    50% {
        transform: translateY(-30px) scale(1.2);
        opacity: 1;
    }

    100% {
        transform: translateY(-60px) scale(0.8);
        opacity: 0;
    }
}