:root {
    --pirate-black: #1a1a1a;
    --pirate-brown: #8B4513;
    --pirate-dark-brown: #654321;
    --pirate-gold: #FFD700;
    --pirate-cream: #F5F5DC;
    --pirate-blue: #4682B4;
    --pirate-red: #DC143C;
    --pirate-black-alpha: rgba(26, 26, 26, 0.9);
    --pirate-brown-alpha: rgba(139, 69, 19, 0.8);
    --pirate-blue-alpha: rgba(70, 130, 180, 0.8);
}

body {
    background: linear-gradient(135deg, var(--pirate-black), var(--pirate-dark-brown));
    color: var(--pirate-cream);
    font-family: 'Roboto Slab', serif;
    min-height: 100vh;
}

.editor-header {
    background: linear-gradient(135deg, var(--pirate-brown-alpha), var(--pirate-blue-alpha));
    padding: 1rem 0;
    border-bottom: 3px solid var(--pirate-gold);
}

.editor-title {
    font-family: '<PERSON>in<PERSON>', serif;
    color: var(--pirate-gold);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.canvas-container {
    position: relative;
    display: inline-block;
    border: 3px solid var(--pirate-gold);
    border-radius: 10px;
    overflow: hidden;
    background: var(--pirate-black);
}

#mapCanvas {
    display: block;
    cursor: crosshair;
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
    opacity: 0.3;
}

.controls-panel {
    background: var(--pirate-black-alpha);
    border: 2px solid var(--pirate-gold);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.btn-pirate {
    background: var(--pirate-brown);
    border: 2px solid var(--pirate-gold);
    color: var(--pirate-cream);
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-pirate:hover {
    background: var(--pirate-gold);
    color: var(--pirate-black);
    transform: translateY(-2px);
}

.form-control {
    background: var(--pirate-black-alpha);
    border: 1px solid var(--pirate-gold);
    color: var(--pirate-cream);
}

.form-control:focus {
    background: var(--pirate-black-alpha);
    border-color: var(--pirate-gold);
    color: var(--pirate-cream);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.form-select {
    background: var(--pirate-black-alpha);
    border: 1px solid var(--pirate-gold);
    color: var(--pirate-cream);
}

.form-select:focus {
    background: var(--pirate-black-alpha);
    border-color: var(--pirate-gold);
    color: var(--pirate-cream);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.items-list {
    max-height: 400px;
    overflow-y: auto;
    background: var(--pirate-black-alpha);
    border: 1px solid var(--pirate-gold);
    border-radius: 10px;
    padding: 1rem;
}

.item-entry {
    background: var(--pirate-brown-alpha);
    border: 1px solid var(--pirate-gold);
    border-radius: 8px;
    padding: 0.8rem;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.item-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.item-emoji {
    font-size: 1.5rem;
}

.code-output {
    background: var(--pirate-black);
    border: 2px solid var(--pirate-gold);
    border-radius: 10px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: var(--pirate-cream);
    white-space: pre-wrap;
    max-height: 500px;
    overflow-y: auto;
}

.tile-size-info {
    background: var(--pirate-blue-alpha);
    border: 1px solid var(--pirate-gold);
    border-radius: 8px;
    padding: 0.8rem;
    margin-bottom: 1rem;
    text-align: center;
}